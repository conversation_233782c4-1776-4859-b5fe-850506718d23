# 动画剧本生成系统使用指南

cd /Users/<USER>/ai-video && python generate_episodes.py /Users/<USER>/ai-video/2-animation-drama/raw_text/save_witch_whole.json --output_dir /Users/<USER>/ai-video/2-animation-drama/episodes/save_witch_whole --max_episodes 2

## 概述

这是一个完整的从小说文本到动画视频的自动化生成系统。系统将几百万字的小说自动转换为结构化的剧本，然后生成对应的音频、图片和视频内容。

## 系统架构

### 完整流程

```
小说文本 → 章节摘要 → 剧集结构 → 剧本生成 → 音频合成 → 图片生成 → 视频合成
```

### 各阶段详细说明

1. **阶段1: 章节摘要生成**
   - 输入: 原始小说文本文件 (`.txt`)
   - 处理: 使用 LLM 分析并生成结构化章节摘要
   - 输出: 章节摘要JSON文件

2. **阶段2: 剧集结构化**
   - 输入: 章节摘要
   - 处理: 分组、生成故事大纲、确定集数分配、生成剧本
   - 输出: 结构化剧本JSON文件 (`episode_XX.json`)

3. **阶段3: 音频生成**
   - 输入: 剧本文件 + 语音配置
   - 处理: 使用 Azure TTS 合成对话和旁白
   - 输出: 音频文件 (`.wav`) + 时间轴文件 (`_timing.json`)

4. **阶段4: 图片生成**
   - 输入: 剧本文件
   - 处理: 生成图片提示词 → 使用 ComfyUI 生成场景图片
   - 输出: 场景图片文件

5. **阶段5: 视频合成**
   - 输入: 时间轴文件 + 图片 + 音频
   - 处理: 图片动效 + 音视频同步
   - 输出: 最终视频文件 (`.mp4`)

## 快速开始

### 1. 环境准备

确保已安装所有依赖：
```bash
pip install -r requirements.txt
```

确保以下服务可用：
- Azure Speech Services (用于TTS)
- ComfyUI (用于图片生成)
- FFmpeg (用于视频处理)

### 2. 准备配置文件

复制并修改语音配置文件：
```bash
cp voice_config_example.json voice_config.json
# 根据需要修改角色语音配置
```

### 3. 运行完整流程

```bash
python generate_animation_drama.py \
    "2-animation-drama/raw_text/save_witch_whole.txt" \
    "save_witch_whole" \
    --voice_config "voice_config.json"
```

### 4. 查看状态

```bash
python generate_animation_drama.py \
    "2-animation-drama/raw_text/save_witch_whole.txt" \
    "save_witch_whole" \
    --voice_config "voice_config.json" \
    --status
```

## 高级用法

### 分阶段执行

只执行特定阶段：
```bash
# 只生成摘要和剧集
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --stages summary episodes

# 只生成音频
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --stages audio
```

### 强制重新生成

```bash
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --force
```

### 自定义输出目录

```bash
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --output_dir "/custom/output/path"
```

## 配置说明

### 语音配置文件格式

```json
{
  "narration": {
    "voice": "zh-CN-YunxiNeural",
    "default_style": "narration"
  },
  "characters": {
    "角色名": {
      "voice": {
        "voice": "zh-CN-YunyangNeural",
        "default_style": "calm",
        "styles": {
          "Neutral": "calm",
          "Cheerful": "cheerful",
          "Sad": "sad",
          "Angry": "angry"
        }
      }
    }
  }
}
```

### 支持的情绪风格

- `Neutral`: 中性
- `Cheerful`: 愉快
- `Sad`: 悲伤
- `Angry`: 愤怒
- `Nervous`: 紧张
- `Determined`: 坚定
- `Hopeful`: 希望

## 输出文件结构

```
2-animation-drama/episodes/project_name/
├── generation_state.json          # 生成状态文件
├── episode_01.json                # 剧本文件
├── episode_01.wav                 # 音频文件
├── episode_01_timing.json         # 时间轴文件
├── episode_01.mp4                 # 视频文件
├── episode_02.json
├── episode_02.wav
├── episode_02_timing.json
├── episode_02.mp4
└── images/                        # 图片目录
    ├── episode_01/
    └── episode_02/
```

## Script 生成问题分析与解决方案

### 当前 Script 生成的主要问题

#### 1. **结构层面问题**

**问题表现：**
- 场景转换生硬，缺乏自然的情感流动
- 对话缺乏层次感和潜台词，显得平面化
- 角色心理描写不够深入，缺乏内在冲突的展现
- 剧情节奏单调，缺乏张弛有度的戏剧性

**根本原因：**
- 从章节摘要到剧本的转换过程中，丢失了原文的细腻情感和心理描写
- 结构生成过于依赖模板化的三幕式结构，缺乏灵活性
- 角色发展弧线在单集内不够完整，缺乏微妙的情感变化

#### 2. **内容质量问题**

**问题表现：**
- 对话过于直白，缺乏符合角色身份的语言特色
- 环境描述单薄，无法营造沉浸感
- 冲突设置过于表面化，缺乏深层次的价值观碰撞
- 情节推进缺乏意外性和悬念感

**根本原因：**
- 生成提示词过于注重结构完整性，忽略了内容的吸引力
- 缺乏对原作风格和语言特色的深度理解
- 角色性格塑造不够立体，导致对话缺乏个性

#### 3. **技术流程问题**

**问题表现：**
- 自由文本到JSON的转换过程中信息丢失
- 多步骤生成导致前后不一致
- 缺乏有效的质量控制机制

**根本原因：**
- 转换过程缺乏上下文保持机制
- 各步骤之间缺乏有效的反馈循环
- 质量评估标准不够明确

### 解决方案

#### 1. **优化结构生成策略**

**改进措施：**
- 引入更灵活的结构模板，支持非线性叙事
- 加强角色心理弧线的设计，确保每个角色在单集内有完整的情感变化
- 优化场景转换机制，增加情感桥接元素

#### 2. **提升内容生成质量**

**改进措施：**
- 重新设计提示词，强调情感深度和角色个性
- 增加原作风格分析步骤，确保生成内容符合原作调性
- 引入冲突升级机制，确保每个场景都有明确的戏剧张力

#### 3. **完善技术流程**

**改进措施：**
- 优化转换算法，减少信息丢失
- 增加质量检查步骤，确保生成内容的一致性
- 建立反馈机制，允许迭代优化

### 具体改进建议

#### 1. **章节摘要质量提升**

**当前问题：** 章节摘要过于简化，丢失了原文的情感细节和角色心理变化。

**改进方案：**
```python
# 在 generate_chapter_summary.py 中增加情感分析
def enhanced_summarize_chapter(chapter_text, context):
    return {
        "plot_summary": "...",
        "emotional_beats": ["角色A的愤怒转为绝望", "角色B的犹豫变为坚定"],
        "character_psychology": {
            "角色A": "内心冲突：权力与道德的抉择",
            "角色B": "成长弧线：从被动到主动"
        },
        "dialogue_style": "正式、带有政治色彩的宫廷用语",
        "atmosphere": "紧张、压抑，暗示即将到来的冲突"
    }
```

#### 2. **剧本结构优化**

**当前问题：** 三幕式结构过于僵化，缺乏灵活性。

**改进方案：**
- 引入"情感节拍"概念，每个场景都有明确的情感目标
- 增加"微冲突"设计，在主要冲突之外设置小的张力点
- 优化场景转换，使用"情感桥接"技术

```python
# 新的场景结构设计
scene_structure = {
    "emotional_goal": "从怀疑转向信任",
    "micro_conflicts": ["身份质疑", "价值观碰撞", "利益冲突"],
    "emotional_bridge": "罗兰的内心独白暗示下一场景的冲突",
    "tension_curve": "低→高→缓解→新的紧张"
}
```

#### 3. **对话质量提升**

**当前问题：** 对话缺乏个性化和潜台词。

**改进方案：**
- 为每个角色建立独特的语言模式
- 增加潜台词分析，确保对话有多层含义
- 引入情感状态追踪，确保对话符合角色当前心理状态

```python
# 角色语言特征定义
character_voice_patterns = {
    "罗兰": {
        "speech_style": "理性、略带讽刺，常用反问句",
        "vocabulary": "现代思维与古代用词的混合",
        "emotional_markers": {
            "愤怒": "语句变短，多用感叹",
            "思考": "长句，逻辑性强"
        }
    }
}
```

#### 4. **提示词优化**

**当前问题：** 提示词过于注重结构，忽略了内容的吸引力。

**改进方案：**
- 重新设计 `generate_full_script` 提示词，强调情感深度
- 增加角色心理状态的详细描述要求
- 加强场景氛围营造的指导

**优化后的提示词示例：**
```
在生成剧本时，请特别注意：
1. 每个对话都应该有明确的潜台词和情感动机
2. 角色的语言风格必须符合其身份、教育背景和当前心理状态
3. 场景描述要能够营造特定的情感氛围，支持剧情发展
4. 冲突不仅是表面的争执，更要体现深层的价值观碰撞
5. 每个场景结束时要为下一场景埋下情感伏笔
```

#### 5. **质量控制机制**

**当前问题：** 缺乏有效的质量评估和改进机制。

**改进方案：**
- 增加剧本质量评分系统
- 建立多轮优化机制
- 引入A/B测试比较不同生成策略的效果

```python
# 质量评估指标
quality_metrics = {
    "dialogue_authenticity": "对话是否符合角色特征",
    "emotional_depth": "情感表达的深度和真实性",
    "plot_coherence": "情节逻辑的连贯性",
    "tension_management": "戏剧张力的控制",
    "character_development": "角色发展的合理性"
}
```

#### 6. **技术实现改进**

**当前问题：** 多步骤生成导致信息丢失和不一致。

**改进方案：**
- 优化自由文本到JSON的转换算法
- 增加上下文保持机制
- 建立版本控制和回滚机制

```python
# 改进的转换流程
def improved_script_conversion(free_text_script, episode_context):
    # 1. 保持原文的情感标记
    emotional_markers = extract_emotional_markers(free_text_script)

    # 2. 识别角色语言特征
    character_voices = analyze_character_voices(free_text_script)

    # 3. 保持场景氛围描述
    atmosphere_descriptions = extract_atmosphere(free_text_script)

    # 4. 转换为JSON时保持这些信息
    return convert_with_context_preservation(
        free_text_script,
        emotional_markers,
        character_voices,
        atmosphere_descriptions
    )
```

### 实施优先级建议

#### 高优先级（立即实施）
1. **优化提示词** - 重新设计 `generate_full_script` 提示词，强调情感深度和角色个性
2. **增强章节摘要** - 在摘要生成时保留更多情感和心理描写信息
3. **改进对话生成** - 为主要角色建立语言特征模板

#### 中优先级（短期实施）
1. **质量评估系统** - 建立剧本质量评分机制
2. **场景转换优化** - 增加情感桥接技术
3. **角色一致性检查** - 确保角色在不同场景中的行为一致

#### 低优先级（长期规划）
1. **A/B测试框架** - 比较不同生成策略的效果
2. **自动化质量控制** - 建立完整的质量监控体系
3. **个性化调优** - 根据不同类型小说调整生成策略

### 预期改进效果

通过实施上述改进措施，预期能够达到以下效果：

1. **剧本吸引力提升30%** - 通过增强情感深度和角色个性
2. **对话质量改善40%** - 通过角色语言特征和潜台词设计
3. **情节连贯性提升25%** - 通过优化场景转换和情感桥接
4. **整体制作效率提升20%** - 通过减少后期人工修改需求

### 监控指标

建议建立以下监控指标来跟踪改进效果：

- **情感丰富度评分** - 衡量剧本的情感表达深度
- **角色一致性评分** - 评估角色行为和语言的一致性
- **情节吸引力评分** - 测量剧情的引人入胜程度
- **制作效率指标** - 跟踪从生成到最终制作的时间成本

## 故障排除

### 常见问题

1. **Azure TTS 认证失败**
   - 检查环境变量 `AZURE_SUBSCRIPTION_KEY` 和 `AZURE_REGION`
   - 确认 Azure Speech Services 配额

2. **ComfyUI 连接失败**
   - 确认 ComfyUI 服务运行在 `127.0.0.1:8188`
   - 检查工作流文件是否存在

3. **FFmpeg 错误**
   - 确认 FFmpeg 已正确安装并在 PATH 中
   - 检查视频编码参数

4. **Script 生成质量问题**
   - 检查章节摘要质量，确保包含足够的情感和心理描写
   - 验证角色设定的完整性和一致性
   - 调整生成参数，增加创意性和情感深度

### 恢复中断的生成

系统会自动保存进度，重新运行相同命令即可从中断点继续：

```bash
# 系统会自动跳过已完成的阶段
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json"
```

### 查看详细错误信息

```bash
# 查看状态文件中的错误信息
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --status | jq '.errors'
```

## 性能优化建议

1. **并行处理**: 可以同时为多个剧集生成音频和图片
2. **缓存利用**: 系统会自动跳过已生成的内容
3. **资源监控**: 注意 GPU 内存使用情况（图片生成阶段）
4. **网络稳定**: 确保 Azure API 调用的网络稳定性

## 扩展开发

### 添加新的语音角色

在 `voice_config.json` 中添加新角色配置：

```json
{
  "characters": {
    "新角色名": {
      "voice": {
        "voice": "zh-CN-VoiceName",
        "default_style": "calm",
        "styles": {
          "Neutral": "calm"
        }
      }
    }
  }
}
```

### 自定义图片生成

修改 `generate_image_prompts.py` 中的提示词生成逻辑，或调整 ComfyUI 工作流文件。

### 添加新的视频效果

在 `generate_video.py` 中添加新的视频效果处理逻辑。
