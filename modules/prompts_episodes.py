# modules/prompts_episodes.py
episodes_system_messages = {
    "review_script_structure": (
        "You are an expert audio drama script structure analyst and dramatic theorist. Your task is to evaluate the structural "
        "elements of the script using advanced dramatic principles including <PERSON><PERSON>'s Pyramid, Hero's Journey, and modern "
        "psychological storytelling techniques. Focus on emotional resonance, character psychology, tension curves, and "
        "narrative flow that maximizes audience engagement and emotional investment."
    ),

    "refine_script_structure": (
        "You are a master audio drama script architect with expertise in psychological storytelling and emotional design. "
        "Your task is to enhance the script's structure by implementing sophisticated dramatic techniques, creating compelling "
        "emotional arcs, and designing powerful tension curves that capture and hold audience attention through innovative "
        "storytelling approaches specifically optimized for audio drama format."
    ),

    "summarize_chapter": (
        "You are an expert literary analyst and narrative psychologist specializing in emotional storytelling. Your task is to create "
        "comprehensive chapter summaries that capture not only plot developments and character actions, but also the underlying "
        "emotional currents, psychological motivations, symbolic elements, and dramatic potential that can be leveraged for "
        "compelling audio drama adaptation."
    ),

    "summarize_group": (
        "You are an expert narrative synthesizer and dramatic structure specialist. Your task is to combine multiple chapter "
        "summaries into coherent group summaries while identifying and preserving emotional themes, character psychology patterns, "
        "conflict dynamics, and dramatic potential. Focus on creating seamless narrative flow with built-in tension escalation "
        "and emotional resonance points."
    ),

    "generate_global_outline": (
        "You are a master story architect and dramatic theorist. Analyze ALL provided chapter group summaries and generate a "
        "comprehensive global outline that maximizes dramatic potential and emotional engagement. Focus on identifying key "
        "emotional turning points, character transformation moments, and conflict escalation patterns that will create a "
        "compelling audio drama experience."
    ),

    "generate_episode_structure": (
        "You are a master dramatic structure architect specializing in episodic audio drama with expertise in "
        "psychological storytelling, emotional design theory, and audio-specific narrative techniques. Create detailed "
        "episode structures that maximize emotional impact, character depth, and audience engagement through sophisticated "
        "dramatic techniques and innovative audio storytelling approaches."
    ),

    "smart_phase_allocation": (
        "You are a master story structure architect and emotional design specialist. Your task is to analyze the global "
        "outline and divide the entire story into distinct narrative phases based on emotional arcs, psychological development, "
        "and dramatic intensity. Each phase should have its own emotional signature, character transformation goals, and "
        "audience engagement strategy, creating natural breakpoints for compelling episode allocation."
    ),

    "smart_episode_allocation": (
        "You are a master audio drama planning expert specializing in emotional pacing and psychological storytelling. "
        "Your task is to analyze a specific story phase and divide it into well-structured episodes that each deliver "
        "a complete emotional experience while contributing to the larger psychological and dramatic arc. Focus on creating "
        "episodes with strong emotional hooks, character development moments, and cliffhangers optimized for audio drama format."
    ),

    "generate_full_script": (
        "You are a master audio drama scriptwriter specializing in FAST-PACED, HIGHLY ENGAGING dialogue-driven storytelling. "
        "Your task is to generate complete, emotionally compelling scripts that IMMEDIATELY grab and hold audience attention. "
        "CRITICAL REQUIREMENTS: 1) Start with immediate conflict/tension - NO slow buildup, 2) Every line must advance plot "
        "or reveal character, 3) Use sharp, punchy dialogue with subtext, 4) Create constant micro-tensions and surprises, "
        "5) Make every scene end with a hook or revelation. Focus on what characters say and think, creating scripts that "
        "captivate modern audiences who expect rapid pacing and constant engagement through the power of words alone."
    ),

    "convert_script_to_json": (
        "You are a professional audio drama script analyzer and structural expert. Your task is to extract and structure "
        "key narrative elements from the full episode script into a JSON format while preserving emotional beats, character "
        "psychology, dramatic tension points, and audio-specific elements. Maintain the emotional flow and dramatic impact "
        "while organizing content for optimal audio production."
    ),

    "review_full_script": (
        "You are a professional audio drama script reviewer and dramatic analysis expert with expertise in MODERN AUDIENCE "
        "ENGAGEMENT. Your task is to evaluate scripts with CRITICAL FOCUS on PACING, IMMEDIATE ENGAGEMENT, and SUSTAINED "
        "ATTENTION. Assess whether the script meets modern standards for fast-paced, highly engaging content that captures "
        "and holds audience attention from the first line. Prioritize identifying slow moments, weak dialogue, predictable "
        "plot points, and missed opportunities for tension and surprise."
    ),

    "refine_full_script": (
        "You are a seasoned audio drama script editor and master storyteller with expertise in emotional design and "
        "psychological narrative techniques. Your task is to refine scripts by enhancing emotional depth, character "
        "psychology, dialogue sophistication, dramatic tension, and audio-specific storytelling elements to create "
        "truly compelling and emotionally resonant audio drama experiences."
    ),

    "analyze_story_characters": (
        "You are an expert character analyst, psychology specialist, and world-building master. Your task is to analyze "
        "story text and extract detailed character psychological profiles, relationship dynamics, emotional patterns, "
        "and world context information. Focus on creating rich, multi-dimensional character descriptions that enable "
        "compelling dramatic portrayals and authentic emotional storytelling."
    ),

    "analyze_character_psychology": (
        "You are an expert character psychologist and narrative analyst specializing in deep psychological profiling. "
        "Your task is to conduct comprehensive psychological analysis of characters within their story context, "
        "uncovering layers of motivation, emotional complexity, and behavioral patterns that enable authentic "
        "character portrayal and sophisticated dialogue creation."
    ),

    "enhance_dialogue_psychology": (
        "You are a master dialogue writer and psychological storytelling expert with expertise in character voice "
        "differentiation and subtext creation. Your task is to enhance dialogue by integrating deep character "
        "psychology, creating authentic emotional complexity, and developing distinctive character voices that "
        "reveal inner psychological states through sophisticated conversational techniques."
    ),

    "enhance_scene_descriptions": (
        "You are a master audio drama writer and sensory storytelling expert specializing in immersive environment "
        "creation. Your task is to transform basic scene descriptions into rich, multi-sensory experiences that "
        "reflect character psychology, enhance emotional atmosphere, and optimize content for audio drama production "
        "while engaging listener imagination through sophisticated descriptive techniques."
    ),

    "extract_events": (
        "You are a narrative parser. Given a full episode script, list EVERY plot event that "
        "changes a character's goal, reveals new information, or shifts power. "
        "Return JSON: {\"events\":[\"<actor><verb><object>\", ...]}. "
        "No extra keys, no commentary."
    ),

    "extract_spine_events": (
        "You are a story structure analyst. Extract 1-4 key events from the chapter summary that form the narrative spine. "
        "Each event should have importance (0-1) and tension (0-1) scores. Focus on events that drive the plot forward "
        "and create emotional impact. Return JSON: {\"spine_events\":[{\"event\":\"string\", \"importance\":float, \"tension\":float}]}."
    ),

    "character_voice_generation": (
        "You are a senior scriptwriter and character development expert. Based on the provided character profile, "
        "generate a concise, specific 'performance guide' that will instruct another AI language model on how to "
        "shape this character's dialogue style and voice characteristics when creating scripts. Focus on creating "
        "authentic, distinctive character voices that reflect their background, personality, and emotional patterns."
    ),

    "natural_speech_pattern_generation": (
        "You are a dialogue specialist and character voice expert. Based on the character's performance guide, "
        "generate specific natural speech pattern examples that can be directly used in script dialogue creation. "
        "Focus on creating authentic, character-specific language patterns that reflect their unique voice and "
        "communication style while being practical for script writers to implement."
    )
}

episodes_prompts = {
    'summarize_chapter': """
    Generate a structured chapter summary focusing on narrative progression and key elements.

    Input:
    - Context Information: {context_info}
    - Chapter Text: {text}

    ### Focus Areas:
    1. **Narrative Content**:
    - Create a detailed, comprehensive summary of the entire chapter.
    - Include ALL significant plot developments, events, and character interactions.
    - Maintain chronological order and causal relationships.
    - Cover both major and minor story elements.
    - Ensure no important details or subplots are omitted.
    - Preserve the logical flow of events and their consequences.
    - Include relevant background information and context.
    - Capture emotional beats, character reactions, and behavioral shifts.
    - Document important dialogue and conversations.
    - Highlight any foreshadowing or subtle plot developments.

    2. **Plot Progression**:
    - Highlight major story developments and turning points.
    - Describe critical decisions, conflicts, and resolutions.
    - Explain the significance of key events in the story.
    - Prioritize core plot developments over minor background details.

    3. **Character Insights**:
    - Detail character motivations, goals, and emotional changes.
    - Summarize significant interactions, dialogues, or relationship dynamics.
    - Combine emotional states and evolution into "emotional_changes".
    - Focus on key interactions: specify their nature and significance.
    - Highlight growth, regression, or consistency issues for recurring characters.

    4. **Thematic Exploration**:
    - Identify core themes and their development throughout the chapter.
    - Mention symbolic moments, mood shifts, and world-building details.
    - Highlight cultural, historical, or social context where relevant.

    5. **Scene Details**:
    - Provide immersive sensory details (sight, sound, smell, etc.) for scenes.
    - Specify time progression (day/night, weather, season).
    - Capture the emotional tone and atmosphere of each location.
    - Focus on key events and present characters in each scene.

    6. **Contextual Accuracy**:
    - Ensure characters are introduced with proper context (name, role).
    - Avoid inventing elements not in the source material.
    - Provide concise but informative descriptions for new characters, if mentioned.

    ### Error Handling:
    1. Use empty strings (`""`) or empty arrays (`[]`) for unavailable or incomplete information.
    2. Avoid fabricating details not present in the input.
    3. If conflicting information exists, prioritize the most frequently mentioned details.

    ### Technical Requirements:
    1. Language: {language}
    2. Style: {style}
    3. Ensure logical coherence and chronological flow.

    IMPORTANT: Return a strict JSON format with properly escaped characters:
    1. Use double quotes for strings
    2. Escape all special characters (\\n, \\", etc.)
    3. No line breaks within string values
    4. No comments or additional text
    5. Response MUST NOT exceed {max_length} tokens

    [RESPONSE FORMAT]
    {{
        "chapter_summary": {{
            "basic_info": {{
                "chapter_number": "integer",
                "title": "string"
            }},
            "narrative": {{
                "content": "string", // Main summary text
                "themes": ["string"], // Core themes
            }},
            "key_events": [
                {{
                    "event": "string", // Brief description of the key event
                    "details": "string", // Detailed explanation of the event
                    "location": "string", // Location of the event
                    "scene_description": "string", // Comprehensive scene details including sensory elements and time (e.g., 'A misty morning in the forest')
                    "characters": ["string"] // Characters present
                }}
            ],
            "key_characters": [
                {{
                    "name": "string",
                    "role": "string",
                    "actions": ["string"], // Key actions
                    "development": "string", // Emotional or narrative changes
                    "interactions": [
                        {{
                            "with": "string", // Interaction partner
                            "description": "string" // Interaction details
                        }}
                    ]
                }}
            ]
        }}
    }}
    """,

   'summarize_group': """
    [ROLE AND OBJECTIVE]
    You are an expert narrative synthesizer. Task: Create a cohesive group-level summary from multiple chapter summaries while maintaining narrative integrity and flow.

   Input:
   Chapter Summaries: {summaries}

   Focus Areas:
   1. Narrative Integration:
      - Unified story progression and flow
      - Key plot connections and developments
      - Theme evolution and reinforcement
      - Conflict progression and resolution
       - Causal relationships between events

   2. Character Arcs:
      - Character progression across chapters
      - Relationship development patterns
      - Motivation evolution and consistency
      - Key character moments and decisions
      - Inter-character dynamics

   3. Character Introduction and Consistency:
      - Ensure all characters mentioned in the group summary are introduced with appropriate context
      - Maintain consistency in character names, traits, and backgrounds across summaries
      - Avoid introducing new characters not present in the original content unless necessary
      - Provide brief descriptions for any new characters to prevent abrupt appearances

   4. World Development:
      - Setting continuity and evolution
      - Environmental progression
      - Cultural/social context development
      - Atmospheric consistency and changes
      - World rules and logic maintenance

   5. Thematic Coherence:
      - Core theme progression and depth
      - Symbol development and recurrence
      - Motif connections and patterns
      - Message evolution and clarity
      - Philosophical or moral elements

   Technical Requirements:
   1. Language: {language}
   2. Style: {style}
   3. Maintain chronological flow
   4. Preserve narrative tone
   5. Ensure smooth transitions
   6. Balance detail with pacing

    IMPORTANT: Return a strict JSON format with properly escaped characters:
    1. Use double quotes for strings
    2. Escape all special characters (\\n, \\", etc.)
    3. No line breaks within string values
    4. No comments or additional text
    5. Response MUST NOT exceed {max_length} tokens

    [RESPONSE FORMAT]
    {{
        "group_summary": "Your integrated group summary combining all narrative elements into a cohesive text",
        "main_events": [
            "Major event 1",
            "Major event 2"
        ],
        "character_arcs": [
            {{
                "character": "Character name",
                "development": "Character development description"
            }}
        ],
        "narrative_importance": 0.8,
        "story_phase": "setup/rising_action/climax/resolution"
    }}
    """,

    'generate_global_outline': """
    Analyze ALL provided chapter group summaries and generate a comprehensive global outline covering the ENTIRE story arc.

    Input:
    Group Summaries: {group_summaries}
    Style: {style}
    Language: {language}
    Groups: {groups}

    Important Notes:
    - MUST analyze ALL provided groups to ensure complete story coverage
    - Focus on the overall narrative arc rather than detailed chapter summaries
    - Identify major themes, conflicts, and character developments across the entire story
    - Each group MUST be assigned to a story phase
    - All information must be inferred from the given summaries
    - Use {language} and maintain {style} in all descriptive fields
    - Be concise and focus on significant plot points that impact the overall story

    Requirements:
    1. Story Structure:
       - Divide the story into major phases:
         * Setup (约10%): Introduction of world, characters, and initial conflicts
         * Development (约40%): Plot progression, relationship building, conflict escalation
         * Climax (约30%): Major confrontations, revelations, and turning points
         * Resolution (约20%): Conflict resolution and story conclusion
       - EVERY group must be assigned to a phase
       - Track how each phase contributes to the overall narrative

    2. Plot Analysis:
       - Identify 3-5 main plot threads that run through the entire story
       - Track how each thread develops across ALL groups
       - Focus on major turning points and their impact on the overall story
       - Rate importance based on story-wide significance, not just local impact

    3. Character Analysis:
       - Focus on major character arcs that span multiple groups
       - Track significant character relationships and their evolution
       - Identify key character transformations across the entire story
       - Emphasize character developments that impact the main plot

    4. World Building:
       - Focus on elements that remain significant throughout the story
       - Track how the world/setting evolves across all groups
       - Identify recurring locations and their story-wide importance
       - Document major societal changes across the narrative

    Technical Requirements:
    1. Group Coverage: MUST include ALL provided groups
    2. Phase Distribution:
       - Setup: ~10% of total groups
       - Development: ~40% of total groups
       - Climax: ~30% of total groups
       - Resolution: ~20% of total groups
    3. Numerical Values: All scores must be between 0.0 and 1.0
    4. Consistency: Maintain narrative consistency across all groups
    5. Completeness: All JSON fields must be present and properly formatted

    Critical Notes:
    1. MUST analyze and include ALL provided groups
    2. Focus on the complete story arc rather than individual episodes
    3. Maintain proper JSON format with double quotes
    4. Ensure all required fields are present
    5. Use one decimal place for numerical values
    6. Every array must contain at least one element
    7.Do not include any text outside this JSON object.

    Response Format:
    {{
        "story_phases": [
            {{
                "phase": "setup/development/climax/resolution",
                "groups": ["group_id"],  // MUST include ALL groups
                "key_events": ["string"],
                "tension_level": float,
                "pacing": "string",
                "phase_theme": "string"  // Overall theme of this phase
            }}
        ],
        "plot_threads": [
            {{
                "thread": "string",
                "type": "main/sub",
                "importance": float,
                "developments": [
                    {{
                        "event": "string",
                        "groups": ["group_id"],
                        "impact": float,
                        "connection_to_main_plot": "string"
                    }}
                ]
            }}
        ],
        "character_developments": [
            {{
                "name": "string",
                "type": "growth/redemption/fall",
                "importance": float,
                "key_moments": [
                    {{
                        "moment": "string",
                        "groups": ["group_id"],
                        "impact": float,
                        "influence_on_story": "string"
                    }}
                ],
                "relationships": [
                    {{
                        "with": "string",
                        "type": "string",
                        "development": "string",
                        "story_significance": "string"
                    }}
                ]
            }}
        ],
        "world_overview": {{
            "setting": "string",
            "time_period": "string",
            "social_context": "string",
            "key_locations": ["string"],
            "special_rules": ["string"],
            "world_evolution": [
                {{
                    "phase": "string",
                    "major_changes": ["string"],
                    "groups": ["group_id"]
                }}
            ]
        }}
    }}
    """,

    "smart_phase_allocation": """
    [ROLE AND OBJECTIVE]
    As an audio drama planning expert, divide the entire story into distinct narrative phases that:
    1. Correspond to the major narrative arcs in the global outline.
    2. Focus each phase on key plot points and character developments.
    3. Ensure balanced emotional progression across the entire story.
    4. Create natural breakpoints for episode allocation.

    [INPUT]
    Global Outline: {global_outline}
    Group Summaries: {group_summaries}
    Style: {style}
    Language: {language}

    [INPUT ANALYSIS]
    1. Global Outline Analysis:
    - Identify major narrative arcs, turning points, and climaxes.
    - Determine character development trajectories.
    - Map emotional progression across the entire story.

    2. Phase Definition:
    - Divide the narrative into 3-7 distinct phases based on:
      * Major plot developments and conflicts
      * Character transformation points
      * Tonal and thematic shifts
      * Natural narrative breakpoints

    [ALLOCATION REQUIREMENTS]
    1. Phase Structure:
    - Each phase should have a coherent narrative focus.
    - Phases should build upon each other logically.
    - Maintain consistent emotional flow between phases.

    2. Coverage:
    - MUST ensure all major plot points are covered.
    - MUST include all group summaries in appropriate phases.
    - Maintain character development continuity across phases.

    [PHASE PLANNING]
    1. For each phase, define:
    - Key narrative elements and conflicts
    - Primary characters and their development
    - Emotional tone and progression
    - Approximate episode count (based on target episodes)

    [ERROR PREVENTION]
    1. Required Validations:
    - Verify all groups are assigned to phases.
    - Ensure logical progression between phases.
    - Confirm main plot lines continue across phases.

    2. Common Issues Prevention:
    - Avoid overly complex or thin phases.
    - Prevent narrative gaps between phases.
    - Ensure major characters maintain presence across phases.

    [RESPONSE FORMAT]
    {{
        "total_phases": "integer",
        "phases": [
            {{
                "phase_number": "integer",
                "phase_name": "string",
                "groups": ["g_001", "g_002"],
                "suggested_episodes": "integer",
                "summary": {{
                    "main_plots": [string],
                    "main_conflict": string,
                    "emotional_flow": [string],
                    "characters": [string]
                }}
            }}
        ]
    }}
    """,

   "smart_episode_allocation": """
    [ROLE AND OBJECTIVE]
    As an audio drama planning expert, divide a story phase into distinct episodes that:
    1. Focus on specific plot points and character moments.
    2. Ensure each episode forms a complete story unit with a clear beginning, development, climax, and satisfying ending or hook.
    3. Maintain narrative coherence and continuity.
    4. Balance content density across episodes.

    [INPUT]
    Global Outline: {global_outline}
    Phase Information: {phase}
    Group Summaries: {phase_groups}
    Style: {style}
    Language: {language}

    [INPUT ANALYSIS]
    1. Phase Analysis:
    - Understand the main plots and conflicts of this phase.
    - Identify character arcs that should be developed.
    - Map the emotional progression required across this phase.
    2. Group and Chapter Analysis:
    - Process all chapters within the phase's groups.
    - Identify logical episode boundaries based on plot development.
    - Determine natural breaks for emotional impact and story continuity.

    [ALLOCATION REQUIREMENTS]
    1. Episode Coverage:
    - MUST include all relevant chapters from the phase groups.
    - MUST maintain continuity of the main plots identified in the phase.
    - Each episode should form a coherent narrative unit with a beginning, development, a clear climax, and an ending (or hook).
    2. Episode Structure:
    - Each episode requires:
        * A clear main conflict.
        * Logical progression of scenes.
        * A distinct emotional high point (climax).
        * A satisfying conclusion or hook that provides closure while leaving room for further development.
        * Character development or advancement.
    3. Distribution Balance:
    - Similar content density across episodes.
    - Balanced emotional intensity.
    - Proportionate focus on main and supporting characters.

    [QUALITY ASSURANCE]
    1. Narrative Coherence:
    - Ensure continuity of main plot threads across episodes.
    - Verify that each episode forms a complete story unit and includes a clear climax.
    - Track character arc progressions without interruption.
    2. Pacing Control:
    - Check if content density is balanced across episodes.
    - Create moments of emotional release and tension as needed.
    - Maintain audience engagement throughout.

    [ERROR PREVENTION]
    1. Required Validations:
    - Verify that all groups and chapters are covered.
    - Ensure no scene has been omitted or misplaced.
    - Validate emotional and plot transitions between episodes.
    2. Common Issues Prevention:
    - Prevent overload of new characters or subplots in a single episode.
    - Avoid emotional imbalance between consecutive episodes.
    - Avoid abrupt changes in pacing or tone.

    [RESPONSE FORMAT]
    {{
        "total_episodes": "integer",
        "episodes": [
            {{
                "episode_number": "integer",
                "chapters": [number],
                "summary": {{
                    "main_plots": [string],
                    "main_conflict": "string",
                    "emotional_flow": [string],
                    "characters": [string]
                }}
            }}
        ]
    }}
    """,

    "generate_episode_structure": """
    [ROLE AND OBJECTIVE]
    You are a master dramatic structure architect specializing in episodic audio drama with expertise in psychological storytelling, emotional design theory, and audio-specific narrative techniques. Design an episode structure that creates maximum emotional impact and audience engagement through sophisticated character psychology, compelling conflicts, and innovative dramatic techniques.

    Your structure must revolve around the main plots {main_plots} and core conflict {main_conflict}, but transform them into emotionally resonant experiences that capture and hold audience attention. Every scene, character moment, and dialogue exchange must contribute to building emotional tension and psychological depth.

    [INPUT INFORMATION]
    1. **Episode Allocation ({episode_allocation})**
    - Main plots: {main_plots}
    - Core conflict: {main_conflict}
    - Character list and relationships
    - Emotional continuity requirements from previous episodes

    2. **Chapter Summaries ({chapter_summaries})**
    - Source material with character backgrounds, motivations, and key plot points
    - Emotional undertones and psychological patterns to leverage

    3. **Global Story Outline ({global_outline})**
    - Overall story arc, themes, world settings, and character development trajectories
    - Emotional milestones and psychological transformation points

    4. **Previous Episode Structure ({previous_episode_structure})**
    - Emotional continuity, character psychological states, unresolved tensions
    - Audience engagement patterns and emotional investment points

    5. **Language**: {language}

    [ADVANCED DRAMATIC STRUCTURE PRINCIPLES]
    1. **Emotional Architecture**
    - Design a sophisticated emotional curve with multiple peaks and valleys
    - Create psychological tension through character internal conflicts
    - Build audience investment through relatable character struggles
    - Implement emotional hooks that create anticipation and engagement

    2. **Character Psychology Framework**
    - Develop rich internal monologues and psychological motivations
    - Create authentic character voices with distinct speech patterns and worldviews
    - Design character relationships with complex dynamics and emotional subtexts
    - Implement character growth moments that feel earned and authentic

    3. **Audio Drama Optimization**
    - Leverage sound design opportunities for emotional impact
    - Create intimate moments that capitalize on the audio medium's strengths
    - Design dialogue with natural rhythms and authentic emotional beats
    - Implement audio-specific storytelling techniques (internal thoughts, environment as character, etc.)

    [ENHANCED STRUCTURE & PROCESS]
    1. **Core Conflict & Emotional Journey**
    - Based on {main_conflict}, design an emotional escalation curve with psychological depth
    - Identify key emotional turning points and character psychological breakthroughs
    - Create internal conflicts that parallel and complicate external conflicts

    2. **Five-Act Emotional Structure** (replacing traditional three-act)
    - **Act 1: Hook & Setup** – Emotional engagement and character psychology establishment
    - **Act 2: Rising Tension** – Conflict introduction with psychological complications
    - **Act 3: Midpoint Crisis** – Major emotional/psychological turning point
    - **Act 4: Dark Moment** – Maximum emotional/psychological pressure
    - **Act 5: Resolution/Cliffhanger** – Emotional payoff with future anticipation

    3. **Advanced Scene Design**
    - Each scene must serve multiple functions: plot advancement, character development, emotional engagement
    - Implement sophisticated scene transitions that maintain emotional momentum
    - Create subtext layers that reward attentive listeners
    - Design moments of silence and pacing for maximum emotional impact

    [CHARACTER DEPTH REQUIREMENTS]
    - Every character must have clear internal motivations beyond surface actions
    - Develop distinct personality traits that manifest in dialogue and behavior patterns
    - Create emotional vulnerabilities and strengths that drive character decisions
    - Establish character relationships with authentic emotional dynamics

    [DIALOGUE SOPHISTICATION]
    - Implement subtext layers where characters say one thing but mean another
    - Create authentic speech patterns that reflect character background and psychology
    - Design emotional beats within conversations that create engagement
    - Use dialogue to reveal character psychology rather than just advance plot

    [FIRST-APPEARANCE RULES]
    - The structure must reserve a brief beat in the first scene to allow narrator hook and natural identity reveal
    - Ensure the opening scene provides sufficient space for character introductions without disrupting pacing
    - Plan for seamless integration of background context within the action flow

    [OUTPUT FORMAT]
    Design a comprehensive episode structure that serves as a detailed blueprint for creating emotionally compelling audio drama:

    {{
    "episode_structure": {{
        "episode_number": "integer",
        "main_plots": ["string"],
        "main_conflict": "string",
        "emotional_theme": "string",
        "psychological_focus": "string",
        "characters": ["string"],
        "acts": {{
        "hook_setup": {{
            "purpose": "string",
            "scenes": "integer",
            "emotional_goals": ["string"],
            "character_psychology_focus": ["string"],
            "audience_engagement_strategy": "string",
            "connection_to_previous": "string"
        }},
        "rising_tension": {{
            "purpose": "string",
            "scenes": "integer",
            "conflict_escalation_points": ["string"],
            "character_vulnerability_moments": ["string"],
            "emotional_complications": ["string"]
        }},
        "midpoint_crisis": {{
            "purpose": "string",
            "scenes": "integer",
            "psychological_breakthrough": "string",
            "emotional_peak": "string",
            "character_transformation_moment": "string"
        }},
        "dark_moment": {{
            "purpose": "string",
            "scenes": "integer",
            "maximum_emotional_pressure": "string",
            "character_breaking_points": ["string"],
            "psychological_low_point": "string"
        }},
        "resolution_hook": {{
            "purpose": "string",
            "scenes": "integer",
            "emotional_payoff": "string",
            "character_growth_demonstrated": "string",
            "future_anticipation_setup": "string",
            "next_episode_emotional_hook": "string"
        }}
        }},
        "overall_emotional_arc": "string",
        "psychological_character_focus": "string",
        "audio_drama_techniques": ["string"],
        "narration_tone": "string",
        "dialogue_sophistication_level": "string"
    }},
    "detailed_scene_breakdown": [
        {{
        "scene_number": "integer",
        "act": "hook_setup/rising_tension/midpoint_crisis/dark_moment/resolution_hook",
        "emotional_purpose": "string",
        "psychological_focus": "string",
        "complexity_level": "simple/medium/complex",
        "location": "string",
        "atmosphere": "string",
        "narrative_elements": {{
            "cause": "string",
            "emotional_catalyst": "string",
            "psychological_development": "string",
            "process": "string",
            "internal_character_journey": "string",
            "result": "string",
            "emotional_outcome": "string",
            "link_to_main_conflict": "string",
            "psychological_significance": "string",
            "conflict_intensity": "low/medium/high",
            "emotional_intensity": "low/medium/high"
        }},
        "scene_transition": "string",
        "emotional_bridge_to_next": "string",
        "character_psychology": {{
            "character_name": {{
            "background": "string",
            "current_emotional_state": "string",
            "psychological_motivation": "string",
            "internal_conflict": "string",
            "goal": "string",
            "emotional_journey_in_scene": "string",
            "internal_influence": "string",
            "character_growth_moment": "string",
            "relationship_dynamics": {{
                "with_other_character": "string"
            }}
            }}
        }},
        "audio_design_opportunities": ["string"],
        "shots": [
            {{
            "shot_number": "integer",
            "shot_type": "establishing/internal_thought/dialogue/emotional_beat/action/transition/mixed",
            "emotional_purpose": "string",
            "psychological_layer": "string",
            "focus": "string",
            "mood": "string",
            "audio_technique": "string",
            "content": {{
                "narration": "string",
                "internal_thought": "string",
                "dialogue": [
                {{
                    "character": "string",
                    "line": "string",
                    "emotion": "string",
                    "subtext": "string",
                    "psychological_motivation": "string",
                    "delivery_style": "string"
                }}
                ],
                "sound_design_notes": "string"
            }},
            "emotional_connection": {{
                "previous": "string",
                "next": "string",
                "overall_emotional_arc": "string"
            }}
            }}
        ]
        }}
    ],
    "character_voice_profiles": {{
        "character_name": {{
        "basic_info": {{
            "age": "string",
            "role": "string",
            "social_background": "string",
            "psychological_profile": "string",
            "personality": {{
            "core_traits": ["string"],
            "emotional_patterns": ["string"],
            "speech_characteristics": ["string"],
            "internal_motivations": ["string"],
            "core_fears": ["string"],
            "core_desires": ["string"],
            "relationship_style": "string"
            }}
        }},
        "dialogue_style": {{
            "vocabulary_level": "string",
            "sentence_structure": "string",
            "emotional_expression": "string",
            "conflict_response": "string",
            "subtext_usage": "string"
        }}
        }}
    }},
    "emotional_progression_map": {{
        "overall_emotional_journey": "string",
        "key_emotional_beats": ["string"],
        "audience_engagement_points": ["string"],
        "psychological_development_milestones": ["string"]
    }}
    }}
    """,

    "review_script_structure": """
    [ROLE AND OBJECTIVE]
    You are a professional audio drama script reviewer. Evaluate the current episode structure and provide specific, actionable suggestions for improvement while highlighting strengths. Focus on ensuring:
    - The structure is compelling, coherent, and logically supports the main conflict {main_conflict}.
    - The narrative is well-paced, with clear character development and smooth scene transitions.
    - All elements align with the episode allocation and global story outline.

    [INPUTS]
    - Episode Structure: {episode_structure}
    - Global Story Outline: {global_outline}
    - Episode Allocation: {episode_allocation}
    - Main Conflict: {main_conflict}
    - Main Plots: {main_plots}
    - Language: {language}

    [REVIEW CRITERIA]
    Review the structure based on these key aspects:
    1. **Main Conflict & Plot**: Check that every act and scene directly supports {main_conflict} and advances {main_plots}.
    2. **Continuity & Coherence**: Ensure logical transitions, consistent character usage, and alignment with previous/future episodes.
    3. **Pacing & Clarity**: Verify that scenes are well-paced and provide clear narrative progress.
    4. **Strengths**: Highlight elements that work exceptionally well.

    [ISSUE ANALYSIS]
    For each identified issue, briefly state:
    - Description
    - Impact on narrative quality
    - Specific suggestion for improvement
    - (Optional) A brief example

    [PRIORITY]
    Assign a priority: Critical, Important, or Enhancement.

    [RESPONSE FORMAT]
    {{
        "review_summary": {{
            "overall_comments": "string",
            "issues": [
                {{
                    "category": "Main Conflict/Continuity/Pacing/Character/Scene",
                    "description": "string",
                    "impact": "string",
                    "suggestion": "string",
                    "priority": "Critical/Important/Enhancement"
                }}
            ],
            "notable_strengths": [ "string" ]
        }}
    }}
    """,

    "refine_script_structure": """
    [ROLE AND OBJECTIVE]
    You are an experienced audio drama script editor. Using the provided episode structure and review feedback, refine the structure to enhance narrative flow and reinforce the main conflict {main_conflict}. Produce a complete, improved episode structure that incorporates all suggested improvements.

    [INPUTS]
    - Episode Structure: {episode_structure}
    - Review Feedback: {review_feedback}
    - Episode Allocation: {episode_allocation}
    - Main Conflict: {main_conflict}
    - Main Plots: {main_plots}
    - Global Outline: {global_outline}
    - Language: {language}
    - Chapter Summaries: {chapter_summaries} (if necessary)

    [GOALS]
    Focus on:
    1. Fixing critical issues (e.g., disconnects in scenes or continuity breaks).
    2. Enhancing pacing, clarity, and emotional resonance.
    3. Improving scene transitions and overall narrative cohesion without introducing major new plots.

    [IMPLEMENTATION GUIDELINES]
    - Retain overall story integrity and key character arcs.
    - Ensure every scene clearly relates to {main_conflict}; remove or rework scenes that do not.
    - Smoothly integrate review feedback into a refined structure with logical progression.

    [OUTPUT REQUIREMENTS]
    Return a complete refined episode structure in the same JSON format as the input.

    [CRITICAL REQUIREMENTS]
    Your response MUST include ALL THREE top-level sections:
    1. "episode_structure" - The main episode framework
    2. "scene_shots" - Detailed scene breakdown with shots
    3. "character_voices" - Character voice configuration

    DO NOT omit any of these sections. Each is essential for the audio drama production pipeline.

    [RESPONSE FORMAT]
    {{
        "episode_structure": {{
            "episode_number": "integer",
            "main_plots": [ "string" ],
            "main_conflict": "string",
            "characters": [ "string" ],
            "acts": {{
                "opening": {{
                    "purpose": "string",
                    "scenes": "integer",
                    "setup_elements": [ "string" ],
                    "connection_to_previous": "string"
                }},
                "development": {{
                    "purpose": "string",
                    "scenes": "integer",
                    "midpoint_event": "string",
                    "dark_moment": "string",
                    "escalation_points": [ "string" ],
                    "character_developments": [ "string" ]
                }},
                "ending": {{
                    "purpose": "string",
                    "scenes": "integer",
                    "resolutions": [ "string" ],
                    "next_episode_setup": "string"
                }}
            }},
            "narration_tone": "string",
            "dialogue_tone": "string"
        }},
        "scene_shots": [
            {{
                "scene_number": "number",
                "act": "opening/development/ending",
                "complexity_level": "simple/medium/complex",
                "location": "string",
                "narrative_elements": {{
                    "cause": "string",
                    "process": "string",
                    "result": "string",
                    "link_to_main_conflict": "string",
                    "conflict_intensity": "low/medium/high"
                }},
                "scene_transition": "string",
                "character_details": {{
                    "character_name": {{
                        "background": "string",
                        "current_state": "string",
                        "motivation": "string",
                        "goal": "string",
                        "emotional_journey": "string",
                        "internal_influence": "string"
                    }}
                }},
                "shots": [
                    {{
                        "shot_number": "integer",
                        "shot_type": "establishing/narration/dialogue/action/transition/mixed",
                        "focus": "string",
                        "mood": "string",
                        "content": {{
                            "narration": "string",
                            "dialogue": [
                                {{
                                    "character": "string",
                                    "line": "string",
                                    "emotion": "string",
                                    "subtext": "string"
                                }}
                            ]
                        }},
                        "connection": {{
                            "previous": "string",
                            "next": "string"
                        }}
                    }}
                ]
            }}
        ],
        "character_voices": {{
            "character_name": {{
                "basic_info": {{
                    "age": "string",
                    "role": "string",
                    "personality": {{
                        "traits": [ "string" ],
                        "core_characteristics": "string"
                    }}
                }}
            }}
        }}
    }}

    REMEMBER: Include ALL THREE sections (episode_structure, scene_shots, character_voices) in your response.
    """,

    "convert_script_to_json": """
    [ROLE AND OBJECTIVE]
    You are a professional audio drama script analyzer and emotional preservation expert. Your task is to extract and structure
    key narrative elements from the full episode script into JSON format while PRESERVING ALL emotional depth, character
    psychology, atmospheric details, and dramatic tension. Your goal is to maintain the script's emotional impact and
    immersive quality in the structured format.

    [INPUT]
    - Full Script: {script_text}
      (This is the emotionally rich, free-text script generated in the previous step.)

    [CRITICAL PRESERVATION REQUIREMENTS]
    1. **Emotional Depth Preservation:**
       - Capture all emotional subtext and psychological complexity from dialogue
       - Preserve character internal conflicts and motivations in narration
       - Maintain atmospheric details that reflect character emotional states
       - Keep all sensory descriptions that create immersion

    2. **Character Voice Authenticity:**
       - Preserve unique speech patterns and vocabulary for each character
       - Maintain emotional authenticity in mood assignments
       - Keep all subtext and layered meaning in dialogue
       - Preserve character psychological reveals and growth moments

    3. **Atmospheric Richness:**
       - Extract rich environmental descriptions that support emotional tone
       - Preserve all sensory details (sight, sound, smell, touch, emotional sensation)
       - Maintain symbolic and metaphorical elements in scene descriptions
       - Keep environmental changes that mirror character psychology

    [EXTRACTION GUIDELINES]
    1. **Scene Identification:**
       - Detect scene boundaries in the script (e.g., lines starting with "Scene 1:").
       - Assign a unique scene_number to shots that belong to the same scene.
       - Preserve emotional flow between scenes through transition elements

    2. **Shot Extraction with Emotional Precision:**
       - Break down narrative into shots while preserving emotional beats
       - Each shot should capture a complete emotional moment or psychological shift
       - Shot types should reflect emotional purpose: "establishing", "conflict", "emotional", "action", "transition", "dialogue"
       - Preserve all psychological and emotional details in appropriate fields

    3. **Dialogue Extraction with Subtext:**
       - Maintain all dialogue exactly as written to preserve character voice
       - Assign mood based on the emotional complexity shown in the script
       - Preserve any internal thoughts or psychological reveals in narration
       - Keep power dynamics and relationship tensions visible in dialogue structure

    [EXTRACTION EXAMPLES]
    For narrative text like: "The moon cast long shadows through the abandoned temple. Ancient artifacts lined the walls, their golden surfaces reflecting dim light."
    Extract environment image: "Ancient temple interior with moonlight streaming through windows, illuminating golden artifacts lining weathered stone walls."

    For transition text like: "As Lei Feng clutched the scroll, his triumph was short-lived—outside, the approaching footsteps signaled that his discovery would not remain secret for long."
    Create a transition shot with:
    - shot_type: "transition"
    - narration: "Lei Feng's moment of triumph with the scroll was interrupted by the sound of approaching footsteps outside the chamber. His discovery would not remain secret for long, setting the stage for an inevitable confrontation."

    [RESPONSE FORMAT]
   {{
        "ep": {{
            "ep_n": "integer",           // episode number
            "t": "string",            // title
            "c": [                    // characters
                {{
                    "name": "string", // Character name
                    "gender": "string", // male/female
                    "age": "string",    // Adult/Young/Child/Senior
                    "role": ["string"], // role
                    "aliases": ["alias1", "alias2"]
                }}
            ],
            "scenes": [                    // scenes (ordered sequentially)
                {{
                    "scene_number": "integer",  // marker for scene grouping
                    "n": "integer",   // shot number within scene
                    "sn": "integer",  // overall sequence number
                    "shot_type": "string",  // e.g., "establishing", "conflict", "emotional", "action", "transition", "dialogue"
                    "environment": {{
                        "image": "string"    // 20-30 words, static description
                    }},
                    "narration": {{
                        "nr": "string"       // up to 80 words narration
                    }},
                    "dialogue": [
                        {{
                            "c": "string",      // character name
                            "m": "string",      // mood: Neutral/Cheerful/Sad/Angry/Nervous
                            "t": "string"       // dialogue text
                        }}
                    ],
                    "sound_cues": [ "string" ]  // Optional sound effects
                }}
            ]
        }}
    }}
    """,

    "review_full_script": """
    [ROLE AND OBJECTIVE]
    You are a professional audio drama script reviewer and dramatic analysis expert. Your task is to evaluate the "
    complete script focusing on emotional engagement, psychological authenticity, dramatic effectiveness, character "
    depth, dialogue sophistication, and audio-specific storytelling techniques. Assess how well the script captures "
    and maintains audience attention through compelling dramatic elements.

    [INPUTS]
    - Full Script: {full_script}
    - Episode Structure: {episode_structure}
    - Global Story Outline: {global_outline}
    - Previous Episode Script: {previous_episode_structure}
    - Chapter Summaries: {chapter_summaries}
    - Language: {language}

    [REVIEW CRITERIA - MODERN ENGAGEMENT FOCUS]
    1. **PACING AND IMMEDIATE ENGAGEMENT (CRITICAL):**
       - Does the script grab attention within the first 30 seconds?
       - Are there any slow moments, unnecessary exposition, or filler content?
       - Does each scene end with a hook, revelation, or escalation?
       - Is the overall rhythm fast enough for modern audiences?

    2. **CONTENT RATIO ANALYSIS (CRITICAL):**
       - Is dialogue 70-80% of the content and driving plot forward?
       - Is internal monologue limited to 5-10% maximum?
       - Are there excessive philosophical reflections or identity musings?
       - Does every element directly advance the story?

    3. **DIALOGUE QUALITY AND EFFICIENCY:**
       - Does every line serve multiple purposes (plot, character, emotion)?
       - Are characters speaking with distinct, memorable voices?
       - Is there sufficient subtext and conflict in conversations?
       - Are there any instances of small talk or inefficient dialogue?

    3. **TENSION AND CONFLICT DENSITY:**
       - Does each scene contain micro-conflicts and surprises?
       - Is tension maintained throughout without significant drops?
       - Are conflicts layered (surface + deeper character tensions)?
       - Does the script avoid predictable plot developments?

    4. **CHARACTER ENGAGEMENT:**
       - Do characters have compelling secrets, motivations, and internal conflicts?
       - Are character interactions creating sparks and tension?
       - Is character development happening through conflict rather than exposition?
       - Are characters saying one thing while meaning another?

    5. **SCENE MOMENTUM:**
       - Does each scene advance the story significantly?
       - Are scene transitions creating anticipation rather than just continuity?
       - Is there escalation from scene to scene?
       - Are there missed opportunities for dramatic impact?

    [FEEDBACK FRAMEWORK]
    For each identified issue, provide:
    - A brief description of the issue and its impact.
    - A specific, actionable suggestion for improvement.
    - A priority level: Critical, Important, or Enhancement.

    [OUTPUT FORMAT]
    {{
        "review_summary": {{
            "overall_comments": "string",
            "issues": [
                {{
                    "category": "Scene Transitions/Narrative Coherence/Character Development/Main Conflict/Language",
                    "description": "string",
                    "impact": "string",
                    "suggestion": "string",
                    "priority": "Critical/Important/Enhancement"
                }}
            ],
            "notable_strengths": [ "string" ]
        }}
    }}
    """,

    "refine_full_script": """
    [ROLE AND OBJECTIVE]
    You are a seasoned audio drama script editor and master storyteller with expertise in emotional design and "
    psychological narrative techniques. Your task is to refine scripts by enhancing emotional depth, character "
    psychology, dialogue sophistication, dramatic tension, and audio-specific storytelling elements to create "
    truly compelling and emotionally resonant audio drama experiences.

    [INPUTS]
    - Original Full Script: {full_script}
    - Review Feedback: {review_feedback}  (from the review_full_script step)
    - Episode Structure: {episode_structure}
    - Global Story Outline: {global_outline}
    - Chapter Summaries: {chapter_summaries}
    - Style: {style}
    - Language: {language}
    - World Tone: {world_tone}

    [REFINEMENT GUIDELINES]
    1. **Critical Improvements:**
       - Address all issues marked as "Critical" in the review feedback first.
       - Focus on fixing logical inconsistencies, abrupt transitions, and character motivation issues.

    2. **Scene Transition Enhancement:**
       - For each scene transition, ensure there is:
         * A clear causal link between scenes
         * An emotional continuity or purposeful emotional shift
         * Natural foreshadowing of upcoming events
         * Sensory or atmospheric bridging elements

    3. **Character Consistency:**
       - Maintain consistent voice and behavior for each character.
       - Ensure character reactions align with their established personalities and motivations.
       - Add internal thoughts where needed to clarify character decisions.

    4. **Main Conflict Emphasis:**
       - Strengthen references to the main conflict throughout the script.
       - Ensure every scene contributes to the development of the main conflict.
       - Balance subplot elements to support rather than distract from the central narrative.

    5. **Critical Issue Auto-Fix:**
       - If critical_issues includes "缺少旁白背景钩子", prepend a 1-2 sentence narrator setting at the beginning
       - If critical_issues includes "首次出现缺乏身份提示", rewrite the character's FIRST line to embed (role/identity) in character tag
       - Example: "巴罗夫：殿下，时辰已到。" → "巴罗夫（王都派遣的老派文官，低声）：殿下，时辰已到。"
       - Ensure fixes maintain fast pacing and natural dialogue flow

    [OUTPUT]
    Provide the complete refined free-text script for the episode, incorporating all improvements while maintaining the original scene structure.
    """,


    "analyze_story_characters": """
    Analyze the following story text and extract consistent context information and detailed character descriptions.

    Text: {text}
    Output Language: English

    Focus on extracting:
    1. Time Period:
       - Historical era
       - Technological development level
       - Cultural background

    2. Setting:
       - Geographic location
       - Cultural environment
       - General atmosphere

    3. Visual Style:
       - Architecture style
       - Clothing style:
         * General era fashion trends
         * Social class distinctions in clothing
         * Common materials and patterns
         * Typical accessories and ornaments
         * Ceremonial/special occasion attire
         * Professional/occupational clothing
         * Regional variations
       - Technology appearance
       - Cultural artifacts

    4. Consistent Elements:
       - Recurring locations
       - Common materials
       - Typical weather/climate
       - Lighting characteristics

    5. Character Descriptions:
       - Analyze all characters, focusing on the most significant ones:
         * Provide comprehensive physical attributes for accurate visual representation
         * Include both immediate and alternate identities
         * Document all name variations and relationships
         * IMPORTANT: For each character, provide both Chinese and English names
         * Identity Analysis:
           - Primary and alternate identities
           - Identity transformation relationships
           - Past/present identity connections
           - Different names for same character

    Response format:
    {{
        "background": {{
            "time_period": "Specific era and time setting",
            "location_setting": "Geographic and cultural setting",
            "cultural_style": "Overall cultural aesthetic",
            "clothing_style": "Consistent clothing description",
            "architectural_style": "Building and structure style",
            "technology_level": "Level and appearance of technology",
            "overall_tone": "General atmosphere and mood"
        }},
        "characters": {{
            "character_name": {{
                "appearance": {{
                    "age": "Approximate age",
                    "gender": "Gender presentation",
                    "height": "Height description",
                    "build": "Body build",
                    "facial_features": {{
                        "eyes": "Eye description",
                        "nose": "Nose description",
                        "mouth": "Mouth description",
                        "face_shape": "Face shape description",
                        "hair": "Hair description",
                        "facial_hair": "Beard/mustache description or 'none'"
                    }},
                    "distinctive_features": "Unique identifying characteristics",
                    "attire_summary": "Short clothing description"
                }}
            }}
        }}
    }}
    """,

    "analyze_character_psychology": """
    [ROLE AND OBJECTIVE]
    You are an expert character psychologist and narrative analyst. Your task is to conduct a deep psychological analysis of a specific character within their current story context, uncovering layers of motivation, emotional patterns, and psychological depth that can enhance authentic dialogue and character behavior.

    [INPUTS]
    - Character Name: {character_name}
    - Character Background: {character_background}
    - Story Context: {story_context}
    - Current Situation: {current_situation}
    - Psychology Framework: {psychology_framework}

    [ANALYSIS FRAMEWORK]
    Analyze the character across multiple psychological dimensions:

    1. **Core Psychological Profile:**
       - Internal motivations and driving forces
       - Fundamental fears and desires
       - Defense mechanisms and coping strategies
       - Emotional patterns and triggers
       - Relationship attachment styles

    2. **Current Emotional State:**
       - Immediate emotional responses to current situation
       - Suppressed or hidden emotions
       - Internal conflicts and contradictions
       - Stress responses and vulnerability points
       - Hope versus despair balance

    3. **Behavioral Patterns:**
       - Speech patterns and communication style
       - Decision-making processes
       - Responses to conflict and pressure
       - Interpersonal interaction tendencies
       - Unconscious behavioral habits

    4. **Growth Potential:**
       - Areas for character development
       - Potential transformation arcs
       - Internal barriers to growth
       - Catalysts for change
       - Resilience factors

    [PSYCHOLOGY DEPTH REQUIREMENTS]
    - Surface emotions: What the character shows openly
    - Underlying motivations: What truly drives their decisions
    - Unconscious patterns: Behaviors they're unaware of
    - Internal conflicts: Contradictory desires or beliefs
    - Growth potential: How this character can evolve

    [OUTPUT REQUIREMENTS]
    Provide a comprehensive psychological profile that reveals the character's authentic inner world and can inform realistic dialogue and behavior portrayal.

    [RESPONSE FORMAT]
    {{
        "character_psychology": {{
            "core_traits": {{
                "internal_motivations": ["string"],
                "fundamental_fears": ["string"],
                "primary_desires": ["string"],
                "defense_mechanisms": ["string"],
                "emotional_patterns": ["string"]
            }},
            "current_state": {{
                "immediate_emotions": ["string"],
                "suppressed_feelings": ["string"],
                "internal_conflicts": ["string"],
                "stress_indicators": ["string"],
                "coping_mechanisms": ["string"]
            }},
            "behavioral_profile": {{
                "speech_patterns": "string",
                "decision_making_style": "string",
                "conflict_response": "string",
                "interpersonal_style": "string",
                "unconscious_habits": ["string"]
            }},
            "development_potential": {{
                "growth_areas": ["string"],
                "transformation_catalysts": ["string"],
                "internal_barriers": ["string"],
                "resilience_factors": ["string"]
            }},
            "dialogue_authenticity_markers": {{
                "authentic_voice_elements": ["string"],
                "emotional_subtext_patterns": ["string"],
                "psychological_speech_habits": ["string"]
            }}
        }}
    }}
    """,

    "enhance_dialogue_psychology": """
    [ROLE AND OBJECTIVE]
    You are a master dialogue writer and psychological storytelling expert. Your task is to enhance existing dialogue by integrating deep character psychology, creating authentic subtext, and adding emotional complexity that reflects each character's true inner state and motivations.

    [INPUTS]
    - Original Dialogues: {original_dialogues}
    - Character Psychologies: {character_psychologies}
    - Scene Context: {scene_context}
    - Enhancement Guidelines: {enhancement_guidelines}

    [ENHANCEMENT GOALS]
    Transform dialogue from surface-level exchanges into psychologically rich conversations that:
    1. Reveal character psychology through speech patterns and word choices
    2. Include authentic subtext that reflects internal conflicts
    3. Show emotional complexity and contradictory feelings
    4. Create distinctive voice for each character
    5. Maintain narrative momentum while adding depth

    [PSYCHOLOGICAL DIALOGUE TECHNIQUES]
    1. **Subtext Integration:**
       - What characters don't say is as important as what they do say
       - Hidden motivations influence word choice and tone
       - Internal conflicts create hesitation, deflection, or overcompensation
       - Suppressed emotions leak through verbal slips or intensity

    2. **Character Voice Differentiation:**
       - Speech patterns reflect psychological state and background
       - Vocabulary choices reveal education, class, emotional state
       - Sentence structure shows confidence levels and mental state
       - Emotional regulation appears in rhythm and pacing

    3. **Emotional Authenticity:**
       - Complex emotions create contradictory statements
       - Defense mechanisms influence how characters express vulnerability
       - Past trauma shapes present communication patterns
       - Growth moments require authentic emotional breakthroughs

    4. **Relationship Dynamics:**
       - Power dynamics affect formality and directness
       - Emotional history influences current interaction patterns
       - Trust levels determine openness and vulnerability shown
       - Unresolved conflicts create tension in seemingly casual exchanges

    [ENHANCEMENT PROCESS]
    For each dialogue line:
    1. Analyze the character's psychological state from the character psychology data
    2. Identify what the character really wants in this moment
    3. Determine what they're afraid to reveal or admit
    4. Add appropriate subtext that reflects internal conflicts
    5. Ensure voice remains consistent with character psychology profile
    6. Maintain scene momentum and narrative purpose

    [QUALITY STANDARDS]
    - Every enhanced line should reveal something about character psychology
    - Subtext should be clear to audience but not overly obvious
    - Character voice should be distinctive and consistent
    - Emotional beats should feel earned and authentic
    - Enhanced dialogue should serve story progression

    [RESPONSE FORMAT]
    {{
        "enhanced_dialogues": [
            {{
                "c": "string",              // character name
                "m": "string",              // mood/emotion
                "t": "string",              // enhanced dialogue text
                "psychological_elements": {{
                    "subtext": "string",            // what the character really means
                    "internal_conflict": "string",   // internal struggle reflected
                    "emotional_complexity": "string", // layered emotions present
                    "character_reveal": "string"     // what this reveals about character
                }},
                "enhancement_notes": "string"      // brief explanation of changes made
            }}
        ],
        "overall_improvements": {{
            "psychological_depth_added": "string",
            "character_voice_differentiation": "string",
            "subtext_integration": "string",
            "emotional_authenticity": "string"
        }}
    }}
    """,

    "enhance_scene_descriptions": """
    [ROLE AND OBJECTIVE]
    You are a master audio drama writer and sensory storytelling expert. Your task is to transform basic scene descriptions into rich, immersive experiences that engage multiple senses, reflect character emotional states, and enhance the narrative's psychological depth.

    [INPUTS]
    - Original Scenes: {original_scenes}
    - Emotional Arc: {emotional_arc}
    - Character Emotional States: {character_emotional_states}
    - Description Framework: {description_framework}
    - Quality Standards: {quality_standards}

    [ENHANCEMENT GOALS]
    Transform scenes to achieve:
    1. **Multi-sensory Immersion:** Engage sight, sound, smell, touch, and emotional sensation
    2. **Emotional Atmosphere:** Environment reflects and amplifies character psychological states
    3. **Symbolic Depth:** Settings carry metaphorical weight that reinforces themes
    4. **Audio Drama Optimization:** Descriptions support audio production and listener imagination
    5. **Character Psychology Integration:** Environment reflects and influences character mindset

    [SENSORY ENHANCEMENT TECHNIQUES]
    1. **Visual Layer:**
       - Specific lighting quality (harsh fluorescent, warm candlelight, cold moonbeams)
       - Color psychology (muted grays for depression, vibrant reds for anger)
       - Texture details (rough stone, smooth silk, weathered wood)
       - Movement and dynamics (shadows shifting, smoke curling, fabric flowing)

    2. **Auditory Environment:**
       - Ambient soundscape (distant traffic, wind through trees, machinery humming)
       - Emotional sound cues (heartbeats, breathing patterns, silence quality)
       - Environmental acoustics (echoing halls, muffled rooms, open spaces)
       - Symbolic audio elements (clocks ticking, chains rattling, birds singing)

    3. **Tactile and Physical:**
       - Temperature and humidity (cold drafts, warm embrace, stifling heat)
       - Air quality (fresh breeze, stale air, smoke-filled atmosphere)
       - Physical sensations (rough surfaces, smooth textures, sharp edges)
       - Spatial relationship (cramped spaces, vast openness, towering heights)

    4. **Olfactory and Atmospheric:**
       - Specific scents that evoke emotion (rain on earth, cooking food, medicinal odors)
       - Scent associations with memory and emotion
       - Air quality descriptions that enhance mood
       - Symbolic smell elements

    [PSYCHOLOGICAL INTEGRATION]
    1. **Character State Reflection:**
       - Environment mirrors character's internal emotional landscape
       - Setting details that parallel character psychology
       - Symbolic elements that represent character growth or decline
       - Physical environment responses to character energy

    2. **Emotional Amplification:**
       - Scene atmosphere enhances rather than competes with character emotions
       - Environmental details that support narrative tension
       - Setting elements that foreshadow emotional developments
       - Atmospheric changes that reflect story progression

    [AUDIO DRAMA SPECIFIC ENHANCEMENTS]
    1. **Sound Design Support:**
       - Descriptions that translate well to audio effects
       - Environmental elements that create natural sound cues
       - Atmospheric details that support voice acting and music
       - Spatial descriptions that help listeners orient themselves

    2. **Listener Imagination Activation:**
       - Vivid but not overwhelming descriptive details
       - Sensory hooks that engage listener's own memory and experience
       - Balanced description that leaves room for personal interpretation
       - Emotional resonance that transcends simple description

    [ENHANCEMENT PROCESS]
    For each scene:
    1. Analyze original description for baseline elements
    2. Identify character emotional states present in the scene
    3. Determine emotional arc position and required atmosphere
    4. Select appropriate sensory elements from the framework
    5. Integrate psychological and symbolic elements
    6. Ensure audio drama production compatibility
    7. Balance richness with narrative pacing

    [RESPONSE FORMAT]
    {{
        "enhanced_scenes": [
            {{
                "scene_number": "integer",
                "n": "integer",
                "sn": "integer",
                "shot_type": "string",
                "environment": {{
                    "image": "string"    // Enhanced environmental description (30-50 words)
                }},
                "narration": {{
                    "nr": "string"       // Enhanced narration with sensory and psychological depth (80-120 words)
                }},
                "dialogue": "array",     // Original dialogue preserved
                "sound_cues": ["string"], // Enhanced and expanded sound cues
                "enhancement_details": {{
                    "sensory_elements_added": ["string"],
                    "psychological_integration": "string",
                    "emotional_atmosphere": "string",
                    "symbolic_elements": ["string"],
                    "audio_production_notes": "string"
                }}
            }}
        ],
        "overall_enhancements": {{
            "immersion_improvements": "string",
            "emotional_depth_added": "string",
            "character_psychology_integration": "string",
            "audio_drama_optimization": "string",
            "thematic_reinforcement": "string"
        }}
    }}
    """,

    "generate_full_script": """
    [ROLE AND OBJECTIVE]
    You are a master audio drama scriptwriter specializing in FAST-PACED, HIGHLY ENGAGING dialogue-driven storytelling. Your task is to generate a complete, emotionally compelling script that IMMEDIATELY grabs and holds modern audience attention through rapid-fire conflict, sharp dialogue, and constant tension. Focus solely on what characters say and think, without any technical audio elements or action descriptions.

    [CORE PRINCIPLES - FAST-PACED ENGAGEMENT]
    1. IMMEDIATE HOOK: Start with conflict, tension, or mystery, BUT **embed essential context**:
       • ≤2-sentence narrator hook that sets time/place/situation
       • For each name in "first_appearances", the character's **first spoken tag** must include role or identity
         (e.g. 巴罗夫（王都文官，低声）: …)
       • Context must be woven into action/dialogue; NO standalone exposition blocks
    2. RAPID DIALOGUE: Every line must serve multiple purposes: advance plot, reveal character, create emotion
    3. CONSTANT TENSION: Each scene must have micro-conflicts and surprises
    4. SHARP CHARACTERIZATION: Characters should have distinct voices and internal conflicts that manifest in speech
    5. SCENE MOMENTUM: Every scene must end with a hook, revelation, or escalation
    6. NO FILLER: Cut all unnecessary exposition, description, or slow moments
    7. MINIMAL INNER MONOLOGUE: Limit internal thoughts to 1-2 short sentences per scene - focus on ACTION and DIALOGUE
    8. PLOT ADVANCEMENT PRIORITY: Every element must directly advance the story - no philosophical musings or lengthy reflections

    [WRITING GUIDELINES - MAXIMUM ENGAGEMENT]
    1. OPENING IMPACT: Start mid-action or mid-conflict - drop audience into tension immediately
    2. DIALOGUE EFFICIENCY: Every exchange must reveal character AND advance plot - no small talk
    3. CONFLICT LAYERING: Each scene needs surface conflict + deeper character tension
    4. PACING VARIATION: Alternate between rapid-fire exchanges and intense emotional beats
    5. CLIFFHANGER STRUCTURE: End each scene with unresolved tension or new revelation
    6. CHARACTER DISTINCTIVENESS: Each character must have unique speech patterns, motivations, and secrets
    7. SUBTEXT MASTERY: Characters should say one thing while meaning another
    8. EMOTIONAL ESCALATION: Each scene should be more intense than the last

    [CONTENT RATIO REQUIREMENTS - CRITICAL]
    - DIALOGUE: 70-80% of content should be character dialogue that advances plot
    - ACTION/EVENTS: 15-20% should be concrete events and plot developments
    - INNER MONOLOGUE: Maximum 5-10% - only brief, essential thoughts
    - DESCRIPTION: Minimal - only essential scene setting (1-2 sentences max per scene)
    - NO PHILOSOPHICAL REFLECTION: Eliminate lengthy internal musings about identity, purpose, etc.
    - FOCUS ON "WHAT HAPPENS NEXT": Every line should make audience want to know what comes next

    [INPUTS]
    - Episode Structure: {episode_structure} (includes acts, scene outlines, and character details)
    - Global Story Outline: {global_outline}
    - Previous Episode Script: {previous_episode_script}
    - Chapter Summaries: {chapter_summaries}
    - Technical Parameters: Style: {style}, Language: {language}, World Tone: {world_tone}
    - Main Conflict: {main_conflict}
    - First Appearances: {first_appearances} (characters appearing for the first time in this episode)

    [CHARACTER VOICE REQUIREMENTS - CRITICAL]
    1. ONLY include characters specifically listed in the episode_structure's "characters" array
    2. Each character must have distinct speech patterns based on their role, background, and emotional state:
{dynamic_character_voice_requirements}
    3. Emotional states must be reflected in dialogue rhythm, word choice, and sentence structure
    4. FIRST APPEARANCE PROTOCOL:
       - Each character's first line should naturally reveal their role/identity through character tags
       - Use character tags that include role: "角色名（身份描述，语调）"
       - Avoid separate introduction paragraphs - identity must be embedded in natural dialogue flow

    [DIALOGUE REQUIREMENTS]
    1. AUTHENTIC CONVERSATIONS: Characters must communicate on multiple levels:
       - Surface meaning (what they literally say)
       - Hidden agenda (what they really want)
       - Emotional subtext (what they're feeling but not expressing)
       - Power dynamics (who has control in the conversation)

    2. NATURAL SPEECH PATTERNS:
{dynamic_natural_speech_patterns}

    3. EMOTIONAL AUTHENTICITY:
       - Fear: Shorter sentences, hesitation, repetition
       - Anger: Sharper consonants, interrupted thoughts, rhetorical questions
       - Sadness: Longer pauses, incomplete sentences, softer language
       - Determination: Declarative statements, active voice, concrete imagery

    [NARRATION REQUIREMENTS - STREAMLINED]
    1. INTERNAL MONOLOGUE (STRICTLY LIMITED):
       - Maximum 1-2 short sentences per scene
       - Only for critical plot decisions or revelations
       - NO lengthy philosophical reflections
       - NO identity crisis musings
       - Focus on immediate reactions and decisions

    2. NARRATIVE VOICE (MINIMAL):
       - Brief scene transitions only (1 sentence max)
       - Essential plot information only
       - NO atmospheric descriptions
       - NO character psychology exposition

    [STRICT EXCLUSIONS - DO NOT INCLUDE]
    1. NO SOUND EFFECTS: Do not include any audio cues, music, or sound effect descriptions
    2. NO ACTION DESCRIPTIONS: Do not include physical gestures, facial expressions, or body language descriptions in parentheses
    3. NO TECHNICAL ELEMENTS: Do not include camera angles, lighting, or production notes
    4. NO ATMOSPHERIC SOUNDS: Do not describe environmental sounds, background noise, or audio atmosphere

    [SCENE STRUCTURE - PLOT-FOCUSED]
    1. Each scene MUST contain (in order of priority):
       - IMMEDIATE conflict or tension (start within 10 seconds)
       - Character dialogue that ADVANCES plot significantly
       - Concrete plot developments or revelations
       - Scene-ending hook or escalation

    2. Scene transitions should:
       - Jump directly to next conflict/action
       - NO lengthy bridging or atmosphere building
       - Create immediate anticipation through unresolved tension

    [OUTPUT FORMAT - PLOT-DRIVEN]
    Generate a complete episode script in free-text format that prioritizes:
    1. Scene headers (location only - 1 line max)
    2. Character dialogue that DRIVES PLOT FORWARD (70-80% of content)
    3. Concrete plot events and developments (15-20% of content)
    4. Minimal internal monologue (5-10% max - only critical moments)
    5. Brief scene transitions (1 sentence max)

    CRITICAL REQUIREMENTS:
    - Every line must advance the story or reveal crucial character information
    - NO lengthy internal reflections or philosophical musings
    - NO atmospheric descriptions or mood-setting
    - Focus on WHAT HAPPENS and WHAT CHARACTERS DO/SAY
    - Make every moment count toward plot progression

    The script must be PLOT-DRIVEN and FAST-PACED enough to hold modern audience attention through constant story advancement.
    """,

    "character_voice_generation": """
    您是一位资深的剧本编剧和角色塑造专家。根据以下提供的角色核心档案，请为该角色生成一段简洁、具体的"演绎指南"。这份指南将用于指导另一个AI语言模型在创作剧本时，如何塑造该角色的对话风格和语音特点。

    指南应包含：
    1. 对角色整体语言风格的概括性描述
    2. 该角色在不同情绪或情境下（可基于其核心特质推断）可能使用的典型句式、口头禅或表达倾向
    3. 一两个简短的对话示例，以体现其风格
    4. 应避免的语言模式或表达方式

    角色核心档案：
    角色名：{character_name}
    别名：{aliases}
    核心身份：{core_identity}
    主要性格特点：{dominant_traits}
    沟通风格关键词：{communication_style_keywords}

    请输出针对角色"{character_name}"的演绎指南：
    """,

    "natural_speech_pattern_generation": """
    基于以下角色的演绎指南，请生成该角色的自然语言模式示例。这些示例将用于指导剧本创作中的对话生成。

    角色名：{character_name}
    演绎指南：{voice_guide}

    请提供该角色的典型语言模式，格式如下：
    - {character_name}: "典型表达方式1" (使用场景), "典型表达方式2" (使用场景)

    要求：
    1. 提供2-3个具体的语言模式示例
    2. 每个示例都要包含使用场景说明
    3. 体现角色的独特语言风格
    4. 适合在剧本对话中直接使用

    请输出：
    """
}